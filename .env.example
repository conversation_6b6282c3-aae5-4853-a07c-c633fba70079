# Monika 环境配置文件示例
# 复制此文件为 .env 并根据需要修改配置

# 应用环境
ENVIRONMENT=development

# 安全配置
SECRET_KEY=your-super-secret-key-change-this-in-production-must-be-at-least-32-characters
ACCESS_TOKEN_EXPIRE_MINUTES=1440

# 数据库配置
DATABASE_URL=sqlite:///./data/monika.db

# 前端端口配置
FRONTEND_PORT=80

# 备份配置
BACKUP_SCHEDULE=0 2 * * *

# SSL配置（可选）
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem

# 日志级别
LOG_LEVEL=info

# CORS配置
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:80,http://127.0.0.1:3000,http://127.0.0.1:80
