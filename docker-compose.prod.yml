services:
  # 后端服务 - 生产配置
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: monika-backend-prod
    restart: always
    environment:
      - PYTHONPATH=/app
      - DATABASE_URL=sqlite:///./data/monika.db
      - ENVIRONMENT=production
      - SECRET_KEY=${SECRET_KEY:-your-super-secret-key-change-this-in-production}
      - ACCESS_TOKEN_EXPIRE_MINUTES=${ACCESS_TOKEN_EXPIRE_MINUTES:-1440}
    volumes:
      - monika-data:/app/data
    expose:
      - "8000"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - monika-network
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # 前端服务 - 生产配置
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: monika-frontend-prod
    restart: always
    ports:
      - "${FRONTEND_PORT:-80}:80"
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - monika-network
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

  # 反向代理和SSL终止（可选，用于HTTPS）
  nginx-proxy:
    image: nginx:alpine
    container_name: monika-proxy
    restart: always
    ports:
      - "443:443"
    volumes:
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./nginx/proxy.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - frontend
    networks:
      - monika-network
    profiles:
      - ssl

  # 定时备份服务
  backup-scheduler:
    image: alpine:latest
    container_name: monika-backup-scheduler
    restart: always
    volumes:
      - monika-data:/data:ro
      - ./backups:/backups
    environment:
      - BACKUP_SCHEDULE=${BACKUP_SCHEDULE:-0 2 * * *}  # 每天凌晨2点备份
    command: >
      sh -c "
        echo '${BACKUP_SCHEDULE:-0 2 * * *} /bin/sh -c \"cp /data/monika.db /backups/monika-backup-\$\$(date +\\%Y\\%m\\%d-\\%H\\%M\\%S).db && find /backups -name \"monika-backup-*.db\" -mtime +7 -delete\"' | crontab - &&
        crond -f
      "
    profiles:
      - backup

networks:
  monika-network:
    driver: bridge

volumes:
  monika-data:
    driver: local
