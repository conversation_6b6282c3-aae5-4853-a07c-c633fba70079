services:
  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: monika-backend
    restart: unless-stopped
    environment:
      - PYTHONPATH=/app
      - DATABASE_URL=sqlite:///./data/monika.db
    volumes:
      - ./data:/app/data
      - ./backend:/app
    ports:
      - "8000:8000"

    networks:
      - monika-network

  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: monika-frontend
    restart: unless-stopped
    ports:
      - "8080:80"
    depends_on:
      - backend
    networks:
      - monika-network

  # 数据库备份服务（可选）
  db-backup:
    image: alpine:latest
    container_name: monika-backup
    restart: "no"
    volumes:
      - ./data:/data
      - ./backups:/backups
    command: >
      sh -c "
        mkdir -p /backups &&
        cp /data/monika.db /backups/monika-backup-$$(date +%Y%m%d-%H%M%S).db &&
        echo 'Database backup completed'
      "
    profiles:
      - backup

networks:
  monika-network:
    driver: bridge

volumes:
  monika-data:
    driver: local
